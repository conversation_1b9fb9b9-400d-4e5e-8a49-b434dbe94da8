<template>
  <div class="polling-container">
    <wd-segmented :options="list" v-model:value="current" @change="pendingList" v-if="!systemIssue" />
    <div class="content"></div>
    <div class="pagination-section" v-if="total > 0">
      <wd-pagination v-model="params.page" :total="total" :pageSize="params.pageSize" @change="handlePaginationChange" show-icon custom-class="modern-pagination" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { PumpHouseApi } from '@/services/model/pump.house'
import * as CommonApi from '@/services/model/common.js'
import { useToast } from 'wot-design-uni'
import { cache } from '@/utils/cache.js'

const toast = useToast()
const taskList = ref([])
const list = ref(['待巡查任务', '已结束任务'])
const current = ref('待巡查任务')
const systemUserIds = ref([])
const userInfo = cache.get('userInfo')
const systemIssue = computed(() => systemUserIds.value.includes(userInfo.id))

onShow(async () => {
  systemUserIds.value = await getSystem()
  if (systemUserIds.value.includes(userInfo.id)) {
    getTaskList()
  } else {
    pendingList()
  }
})

async function getTaskList() {
  try {
    const { data } = await PumpHouseApi.taskList()
    taskList.value = data
  } catch (error) {
    toast.error('获取任务列表失败')
  }
}

async function pendingList() {
  const IsEnd = current.value === '已结束任务' ? 1 : 0
  const { data } = await PumpHouseApi.pendingList(IsEnd)
  taskList.value = data
}

// 获取字典数据 记录了哪些用户能
async function getSystem() {
  const { data } = await CommonApi.dictionaryLookup('system:patrol:issue')
  const systemUserIds = data.map((item) => Number(item.DictCode))
  // systemUserIds.push(5) // 模拟添加权限用户
  console.log(systemUserIds)

  return systemUserIds
}
</script>

<style lang="less" scoped>
.polling-container {
}
</style>
