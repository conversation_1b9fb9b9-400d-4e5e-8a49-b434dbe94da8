<template>
  <div class="polling-container">
    <!-- 自定义标签切换器 -->
    <div class="tab-switcher" v-if="!systemIssue">
      <div class="tab-container">
        <div v-for="(tab, index) in list" :key="index" class="tab-item" :class="{ active: current === tab }" @click="switchTab(tab)">
          <div class="tab-icon">
            <wd-icon :name="getTabIcon(tab)" size="18px" />
          </div>
          <span class="tab-text">{{ tab }}</span>
          <div class="tab-badge" v-if="getTabCount(tab) > 0">{{ getTabCount(tab) }}</div>
        </div>
        <div class="tab-indicator" :style="{ transform: `translateX(${getIndicatorPosition()}px)` }"></div>
      </div>
    </div>

    <!-- 任务列表内容 -->
    <div class="content-section">
      <scroll-view class="scroll-container" scroll-y="true" v-if="taskList.length > 0">
        <div class="task-list">
          <template v-for="(item, index) in taskList" :key="index">
            <div class="task-card" @click="handleItemClick(item)">
              <!-- 卡片头部 -->
              <div class="card-header">
                <div class="task-title-section">
                  <div class="task-title">{{ item.TaskName || item.title || item.name || '巡查任务' }}</div>
                  <div class="task-subtitle" v-if="item.PumpHouseName">
                    <wd-icon name="location" size="12px" color="#666" />
                    <span>{{ item.PumpHouseName }}</span>
                  </div>
                </div>
                <div class="task-status-badge" :class="getStatusClass(item)">
                  <wd-icon :name="getStatusIcon(item)" size="12px" />
                  <span>{{ getStatusText(item) }}</span>
                </div>
              </div>

              <!-- 进度条 -->
              <div class="progress-section" v-if="getProgressData(item)">
                <div class="progress-info">
                  <span class="progress-label">巡查进度</span>
                  <span class="progress-value">{{ getProgressPercentage(item) }}%</span>
                  <span class="progress-detail">{{ getProgressDetail(item) }}</span>
                </div>
                <div class="progress-bar">
                  <div class="progress-fill" :style="{ width: getProgressPercentage(item) + '%' }" :class="getProgressClass(item)"></div>
                </div>
              </div>

              <!-- 任务详情 -->
              <div class="task-details">
                <div class="detail-row" v-if="item.TaskRemark">
                  <div class="detail-label">
                    <wd-icon name="edit" size="14px" color="#666" />
                    <span>备注</span>
                  </div>
                  <div class="detail-value">{{ item.TaskRemark }}</div>
                </div>

                <div class="detail-row" v-if="item.ModifyTime">
                  <div class="detail-label">
                    <wd-icon name="time" size="14px" color="#666" />
                    <span>更新时间</span>
                  </div>
                  <div class="detail-value">{{ formatTime(item.ModifyTime) }}</div>
                </div>

                <div class="detail-row" v-if="item.TaskEndTime">
                  <div class="detail-label">
                    <wd-icon name="calendar" size="14px" color="#666" />
                    <span>{{ current === '待巡查任务' ? '截止时间' : '完成时间' }}</span>
                  </div>
                  <div class="detail-value" :class="{ countdown: current === '待巡查任务' && isTaskOverdue(item.TaskEndTime) }">
                    {{ current === '待巡查任务' ? getCountdown(item.TaskEndTime) : formatTime(item.TaskEndTime) }}
                  </div>
                </div>
              </div>

              <!-- 卡片底部 -->
              <div class="card-footer">
                <div class="task-meta">
                  <span class="task-id" v-if="item.TaskId">ID: {{ item.TaskId }}</span>
                  <span class="create-time" v-if="item.CreateTime"> 创建于 {{ formatTime(item.CreateTime) }} </span>
                </div>
                <div class="task-arrow">
                  <wd-icon name="arrow-right" size="16px" color="#999" />
                </div>
              </div>
            </div>
          </template>
        </div>
      </scroll-view>

      <!-- 空状态 -->
      <div class="empty-state" v-else>
        <div class="empty-icon">
          <wd-icon name="list" size="48px" color="#cccccc" />
        </div>
        <h3 class="empty-text">暂无任务数据</h3>
        <p class="empty-hint">{{ current === '已结束任务' ? '暂无已结束的任务' : '暂无待巡查的任务' }}</p>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-section" v-if="total > 0">
      <wd-pagination v-model="params.page" :total="total" :pageSize="params.pageSize" @change="handlePaginationChange" show-icon custom-class="modern-pagination" />
    </div>
  </div>

  <wd-toast />
</template>

<script setup>
import { ref, computed, reactive } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { PumpHouseApi } from '@/services/model/pump.house'
import * as CommonApi from '@/services/model/common.js'
import { useToast } from 'wot-design-uni'
import { cache } from '@/utils/cache.js'

const toast = useToast()
const taskList = ref([])
const total = ref(0)
const list = ref(['待巡查任务', '已结束任务'])
const current = ref('待巡查任务')
const systemUserIds = ref([])
const userInfo = cache.get('userInfo')
const systemIssue = computed(() => systemUserIds.value.includes(userInfo.id))

// 任务统计数据
const taskStats = ref({
  pending: 0,
  completed: 0
})

// 分页参数
const params = reactive({ page: 1, pageSize: 1 })

onShow(async () => {
  // 重置分页参数
  params.page = 1

  systemUserIds.value = await getSystem()
  if (systemUserIds.value.includes(userInfo.id)) {
    getTaskList()
  } else {
    pendingList()
  }
})

async function getTaskList() {
  try {
    toast.loading('正在加载...')
    const { data, pagination } = await PumpHouseApi.taskList(params)
    taskList.value = data || []
    total.value = pagination?.total || 0
    toast.close()
  } catch (error) {
    console.error('获取任务列表失败:', error)
    toast.error('获取任务列表失败')
    taskList.value = []
    total.value = 0
    toast.close()
  }
}

async function pendingList() {
  try {
    // 切换标签时重置到第一页
    params.page = 1

    toast.loading('正在加载...')
    const end = current.value === '已结束任务' ? 1 : 0
    const requestParams = { ...params, end }
    const { data, pagination } = await PumpHouseApi.pendingList(requestParams)
    taskList.value = data || []
    total.value = pagination?.total || 0

    // 更新统计数据
    if (current.value === '待巡查任务') {
      taskStats.value.pending = total.value
    } else {
      taskStats.value.completed = total.value
    }

    toast.close()
  } catch (error) {
    console.error('获取任务列表失败:', error)
    toast.error('获取任务列表失败')
    taskList.value = []
    total.value = 0
    toast.close()
  }
}

// 分页处理
function handlePaginationChange({ value }) {
  params.page = value
  if (systemUserIds.value.includes(userInfo.id)) {
    getTaskList()
  } else {
    pendingList()
  }
}

// 点击任务项
function handleItemClick(item) {
  uni.vibrateShort({ type: 'medium' })
  // 这里可以跳转到任务详情页面
  console.log('点击任务:', item)
  // uni.navigateTo({ url: `/pages/task/detail?id=${item.id}` })
}

// 获取状态样式类
function getStatusClass(item) {
  if (current.value === '已结束任务') {
    return 'status-completed'
  }
  // 检查是否逾期
  if (item.TaskEndTime && isTaskOverdue(item.TaskEndTime)) {
    return 'status-overdue'
  }
  return 'status-pending'
}

// 获取状态文本
function getStatusText(item) {
  if (current.value === '已结束任务') {
    return '已完成'
  }
  // 检查是否逾期
  if (item.TaskEndTime && isTaskOverdue(item.TaskEndTime)) {
    return '已逾期'
  }
  return '进行中'
}

// 获取状态图标
function getStatusIcon(item) {
  if (current.value === '已结束任务') {
    return 'check-circle'
  }
  if (item.TaskEndTime && isTaskOverdue(item.TaskEndTime)) {
    return 'warn'
  }
  return 'time'
}

// 判断任务是否逾期
function isTaskOverdue(endTime) {
  if (!endTime) return false
  const now = new Date()
  const end = new Date(endTime)
  return now > end
}

// 获取倒计时文本
function getCountdown(endTime) {
  if (!endTime) return ''

  const now = new Date()
  const end = new Date(endTime)
  const diff = end - now

  if (diff <= 0) {
    return '已逾期'
  }

  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

  if (days > 0) {
    return `剩余 ${days} 天 ${hours} 小时`
  } else if (hours > 0) {
    return `剩余 ${hours} 小时 ${minutes} 分钟`
  } else if (minutes > 0) {
    return `剩余 ${minutes} 分钟`
  } else {
    return '即将到期'
  }
}

// 格式化时间
function formatTime(time) {
  if (!time) return ''
  const date = new Date(time)
  const now = new Date()
  const diff = now - date

  if (diff < 60000) {
    // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) {
    // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) {
    // 1天内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleDateString()
  }
}

// 标签切换
function switchTab(tab) {
  current.value = tab
  pendingList()
}

// 获取标签图标
function getTabIcon(tab) {
  return tab === '待巡查任务' ? 'clock' : 'check-circle'
}

// 获取标签数量
function getTabCount(tab) {
  if (tab === '待巡查任务') {
    return taskStats.value.pending
  }
  return taskStats.value.completed
}

// 获取指示器位置
function getIndicatorPosition() {
  const index = list.value.indexOf(current.value)
  return index * 50 // 每个标签宽度的百分比
}

// 处理进度数据
function getProgressData(item) {
  return item.inspectionProgress || (item.totalPumpRooms && item.completedPumpRooms !== undefined)
}

// 获取进度百分比
function getProgressPercentage(item) {
  if (item.inspectionProgress !== undefined) {
    return Math.round(item.inspectionProgress)
  }

  if (item.totalPumpRooms && item.completedPumpRooms !== undefined) {
    const total = parseInt(item.totalPumpRooms) || 0
    const completed = parseInt(item.completedPumpRooms) || 0
    return total > 0 ? Math.round((completed / total) * 100) : 0
  }

  return 0
}

// 获取进度详情文本
function getProgressDetail(item) {
  if (item.totalPumpRooms && item.completedPumpRooms !== undefined) {
    return `${item.completedPumpRooms}/${item.totalPumpRooms}`
  }
  return ''
}

// 获取进度条样式类
function getProgressClass(item) {
  const percentage = getProgressPercentage(item)
  if (percentage >= 100) return 'progress-complete'
  if (percentage >= 80) return 'progress-high'
  if (percentage >= 50) return 'progress-medium'
  return 'progress-low'
}

// 获取字典数据 记录了哪些用户能
async function getSystem() {
  const { data } = await CommonApi.dictionaryLookup('system:patrol:issue')
  const systemUserIds = data.map((item) => Number(item.DictCode))
  // systemUserIds.push(5) // 模拟添加权限用户
  console.log(systemUserIds)

  return systemUserIds
}
</script>

<style lang="less" scoped>
.polling-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24rpx;
  position: relative;
  overflow: hidden;

  // 添加动态背景效果
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: float 20s infinite linear;
    pointer-events: none;
  }

  &::after {
    content: '';
    position: absolute;
    top: 20%;
    right: -30%;
    width: 60%;
    height: 60%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.05) 0%, transparent 70%);
    animation: float 15s infinite linear reverse;
    pointer-events: none;
  }
}

@keyframes float {
  0% {
    transform: rotate(0deg) translate(-20px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translate(-20px) rotate(-360deg);
  }
}

// 自定义标签切换器样式
.tab-switcher {
  margin-bottom: 24rpx;
}

.tab-container {
  position: relative;
  display: flex;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 24rpx;
  padding: 8rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}

.tab-item {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx 20rpx;
  border-radius: 20rpx;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 2;

  &.active {
    color: #2d3748;
    font-weight: 600;

    .tab-icon {
      color: #4299e1;
    }
  }

  &:not(.active) {
    color: rgba(255, 255, 255, 0.8);

    &:hover {
      color: rgba(255, 255, 255, 0.95);
      background: rgba(255, 255, 255, 0.1);
    }
  }
}

.tab-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.tab-text {
  font-size: 28rpx;
  font-weight: 500;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.tab-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36rpx;
  height: 36rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  border-radius: 18rpx;
  font-size: 20rpx;
  font-weight: 600;
  padding: 0 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
  animation: badge-pulse 2s infinite;
}

@keyframes badge-pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.tab-indicator {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: calc(50% - 8rpx);
  height: calc(100% - 16rpx);
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 1;
}

// 内容区域样式
.content-section {
  flex: 1;
  margin-top: 24rpx;
  overflow: hidden;
}

.scroll-container {
  height: 100%;
  width: 100%;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding-bottom: 20rpx;
}

.task-card {
  background: rgba(255, 255, 255, 0.98);
  border-radius: 24rpx;
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
  border: 1rpx solid rgba(255, 255, 255, 0.9);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  backdrop-filter: blur(10rpx);

  // 添加卡片光泽效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
    transition: left 0.6s ease;
    z-index: 1;
    pointer-events: none;
  }

  &:active {
    transform: translateY(4rpx) scale(0.98);
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);

    &::before {
      left: 100%;
    }
  }

  &:hover::before {
    left: 100%;
  }
}

// 卡片头部
.card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 24rpx 24rpx 16rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
}

.task-title-section {
  flex: 1;
  margin-right: 16rpx;
}

.task-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #1a202c;
  line-height: 1.3;
  margin-bottom: 8rpx;
}

.task-subtitle {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.task-status-badge {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 600;
  white-space: nowrap;

  &.status-pending {
    background: linear-gradient(135deg, #fef3cd 0%, #fde68a 100%);
    color: #d97706;
    border: 1rpx solid rgba(217, 119, 6, 0.2);
  }

  &.status-completed {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    color: #059669;
    border: 1rpx solid rgba(5, 150, 105, 0.2);
  }

  &.status-overdue {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: #dc2626;
    border: 1rpx solid rgba(220, 38, 38, 0.2);
  }
}

// 进度条部分
.progress-section {
  padding: 20rpx 24rpx;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.9) 100%);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1rpx;
    background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.3) 50%, transparent 100%);
  }
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.progress-label {
  font-size: 24rpx;
  color: #4a5568;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6rpx;

  &::before {
    content: '📊';
    font-size: 20rpx;
  }
}

.progress-value {
  font-size: 28rpx;
  color: #2d3748;
  font-weight: 700;
}

.progress-detail {
  font-size: 20rpx;
  color: #6b7280;
  background: rgba(107, 114, 128, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  margin-left: 8rpx;
}

.progress-bar {
  height: 12rpx;
  background: rgba(203, 213, 225, 0.6);
  border-radius: 6rpx;
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  border-radius: 6rpx;
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.3) 0%, transparent 50%, rgba(255, 255, 255, 0.3) 100%);
    animation: progress-shine 2s infinite;
  }

  &.progress-low {
    background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
  }

  &.progress-medium {
    background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
  }

  &.progress-high {
    background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
  }

  &.progress-complete {
    background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  }
}

@keyframes progress-shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

// 任务详情
.task-details {
  padding: 20rpx 24rpx;
}

.detail-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.detail-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
  min-width: 140rpx;
  font-size: 24rpx;
  color: #6b7280;
  font-weight: 500;
}

.detail-value {
  flex: 1;
  font-size: 24rpx;
  color: #374151;
  line-height: 1.5;
  word-break: break-all;

  &.countdown {
    color: #dc2626;
    font-weight: 600;
  }
}

// 卡片底部
.card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 24rpx;
  background: rgba(248, 250, 252, 0.5);
  border-top: 1rpx solid rgba(0, 0, 0, 0.06);
}

.task-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
}

.task-id {
  font-size: 20rpx;
  color: #9ca3af;
  background: rgba(156, 163, 175, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

.create-time {
  font-size: 20rpx;
  color: #9ca3af;
}

.task-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  background: rgba(156, 163, 175, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
  position: relative;
}

.empty-icon {
  margin-bottom: 32rpx;
  opacity: 0.7;
  animation: float-gentle 3s ease-in-out infinite;

  // 添加发光效果
  filter: drop-shadow(0 0 20rpx rgba(255, 255, 255, 0.3));
}

@keyframes float-gentle {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

.empty-text {
  font-size: 32rpx;
  font-weight: 700;
  color: #fff;
  margin: 0 0 16rpx 0;
  line-height: 1.3;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  animation: fade-in-up 0.6s ease-out;
}

.empty-hint {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.5;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
  animation: fade-in-up 0.8s ease-out;
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 分页样式
.pagination-section {
  padding: 16rpx 0;
  background: transparent;
  flex-shrink: 0;
}

// 自定义分页样式
:deep(.modern-pagination) {
  .wd-pagination {
    background: rgba(255, 255, 255, 0.9) !important;
    border-radius: 16rpx !important;
    padding: 16rpx !important;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08) !important;
  }
}

/* 分页样式 - 小程序兼容写法 */
.modern-pagination .wd-pagination {
  background: rgba(255, 255, 255, 0.9) !important;
  border-radius: 16rpx !important;
  padding: 16rpx !important;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08) !important;
}

/* 全局分页样式覆盖 */
.wd-pagination {
  background: rgba(255, 255, 255, 0.9) !important;
  border-radius: 16rpx !important;
  padding: 16rpx !important;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08) !important;
}

// 动画效果
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.task-card {
  animation: slideIn 0.4s ease-out;
}

.countdown {
  animation: pulse 2s infinite;
}

// 响应式设计
@media (max-width: 750rpx) {
  .polling-container {
    padding: 16rpx;
  }

  .card-header {
    padding: 20rpx 20rpx 12rpx;
  }

  .task-title {
    font-size: 28rpx;
  }

  .task-subtitle {
    font-size: 22rpx;
  }

  .task-status-badge {
    font-size: 20rpx;
    padding: 6rpx 12rpx;
  }

  .progress-section {
    padding: 12rpx 20rpx;
  }

  .task-details {
    padding: 16rpx 20rpx;
  }

  .detail-label {
    min-width: 120rpx;
    font-size: 22rpx;
  }

  .detail-value {
    font-size: 22rpx;
  }

  .card-footer {
    padding: 12rpx 20rpx;
  }

  .task-id,
  .create-time {
    font-size: 18rpx;
  }
}

// 深色模式适配（可选）
@media (prefers-color-scheme: dark) {
  .task-card {
    background: rgba(45, 55, 72, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .task-title {
    color: #f7fafc;
  }

  .task-subtitle {
    color: #a0aec0;
  }

  .detail-label {
    color: #a0aec0;
  }

  .detail-value {
    color: #e2e8f0;
  }

  .progress-section,
  .card-footer {
    background: rgba(26, 32, 44, 0.8);
  }
}
</style>
