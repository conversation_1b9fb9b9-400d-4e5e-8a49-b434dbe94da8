<template>
  <div class="polling-container">
    <wd-segmented :options="list" v-model:value="current" @change="pendingList" v-if="!systemIssue" />

    <!-- 任务列表内容 -->
    <div class="content-section">
      <scroll-view class="scroll-container" scroll-y="true" v-if="taskList.length > 0">
        <div class="task-list">
          <template v-for="(item, index) in taskList" :key="index">
            <div class="task-item" @click="handleItemClick(item)">
              <div class="task-content">
                <div class="task-title">{{ item.title || item.name || '任务' }}</div>
                <div class="task-info">
                  <span class="task-status" :class="getStatusClass(item)">
                    {{ getStatusText(item) }}
                  </span>
                  <span class="task-time" v-if="item.createTime">
                    {{ formatTime(item.createTime) }}
                  </span>
                </div>
              </div>
              <div class="task-arrow">
                <wd-icon name="arrow-right" size="16px" color="#999" />
              </div>
            </div>
          </template>
        </div>
      </scroll-view>

      <!-- 空状态 -->
      <div class="empty-state" v-else>
        <div class="empty-icon">
          <wd-icon name="list" size="48px" color="#cccccc" />
        </div>
        <h3 class="empty-text">暂无任务数据</h3>
        <p class="empty-hint">{{ current === '已结束任务' ? '暂无已结束的任务' : '暂无待巡查的任务' }}</p>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-section" v-if="total > 0">
      <wd-pagination v-model="params.page" :total="total" :pageSize="params.pageSize" @change="handlePaginationChange" show-icon custom-class="modern-pagination" />
    </div>
  </div>

  <wd-toast />
</template>

<script setup>
import { ref, computed, reactive } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { PumpHouseApi } from '@/services/model/pump.house'
import * as CommonApi from '@/services/model/common.js'
import { useToast } from 'wot-design-uni'
import { cache } from '@/utils/cache.js'

const toast = useToast()
const taskList = ref([])
const total = ref(0)
const list = ref(['待巡查任务', '已结束任务'])
const current = ref('待巡查任务')
const systemUserIds = ref([])
const userInfo = cache.get('userInfo')
const systemIssue = computed(() => systemUserIds.value.includes(userInfo.id))

// 分页参数
const params = reactive({ page: 1, pageSize: 1 })

onShow(async () => {
  // 重置分页参数
  params.page = 1

  systemUserIds.value = await getSystem()
  if (systemUserIds.value.includes(userInfo.id)) {
    getTaskList()
  } else {
    pendingList()
  }
})

async function getTaskList() {
  try {
    toast.loading('正在加载...')
    const { data, pagination } = await PumpHouseApi.taskList(params)
    taskList.value = data || []
    total.value = pagination?.total || 0
    toast.close()
  } catch (error) {
    console.error('获取任务列表失败:', error)
    toast.error('获取任务列表失败')
    taskList.value = []
    total.value = 0
    toast.close()
  }
}

async function pendingList() {
  try {
    // 切换标签时重置到第一页
    params.page = 1

    toast.loading('正在加载...')
    const end = current.value === '已结束任务' ? 1 : 0
    const requestParams = { ...params, end }
    const { data, pagination } = await PumpHouseApi.pendingList(requestParams)
    taskList.value = data || []
    total.value = pagination?.total || 0
    toast.close()
  } catch (error) {
    console.error('获取任务列表失败:', error)
    toast.error('获取任务列表失败')
    taskList.value = []
    total.value = 0
    toast.close()
  }
}

// 分页处理
function handlePaginationChange({ value }) {
  params.page = value
  if (systemUserIds.value.includes(userInfo.id)) {
    getTaskList()
  } else {
    pendingList()
  }
}

// 点击任务项
function handleItemClick(item) {
  uni.vibrateShort({ type: 'medium' })
  // 这里可以跳转到任务详情页面
  console.log('点击任务:', item)
  // uni.navigateTo({ url: `/pages/task/detail?id=${item.id}` })
}

// 获取状态样式类
function getStatusClass(item) {
  // 根据任务状态返回不同的样式类
  if (current.value === '已结束任务') {
    return 'status-completed'
  }
  return 'status-pending'
}

// 获取状态文本
function getStatusText(item) {
  if (current.value === '已结束任务') {
    return '已完成'
  }
  return '待处理'
}

// 格式化时间
function formatTime(time) {
  if (!time) return ''
  const date = new Date(time)
  const now = new Date()
  const diff = now - date

  if (diff < 60000) {
    // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) {
    // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) {
    // 1天内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleDateString()
  }
}

// 获取字典数据 记录了哪些用户能
async function getSystem() {
  const { data } = await CommonApi.dictionaryLookup('system:patrol:issue')
  const systemUserIds = data.map((item) => Number(item.DictCode))
  // systemUserIds.push(5) // 模拟添加权限用户
  console.log(systemUserIds)

  return systemUserIds
}
</script>

<style lang="less" scoped>
.polling-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24rpx;
}

// 内容区域样式
.content-section {
  flex: 1;
  margin-top: 24rpx;
  overflow: hidden;
}

.scroll-container {
  height: 100%;
  width: 100%;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  padding-bottom: 20rpx;
}

.task-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    background: rgba(255, 255, 255, 0.9);
  }
}

.task-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.task-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
  line-height: 1.4;
}

.task-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.task-status {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;

  &.status-pending {
    background: rgba(255, 193, 7, 0.1);
    color: #faad14;
    border: 1rpx solid rgba(255, 193, 7, 0.2);
  }

  &.status-completed {
    background: rgba(82, 196, 26, 0.1);
    color: #52c41a;
    border: 1rpx solid rgba(82, 196, 26, 0.2);
  }
}

.task-time {
  font-size: 22rpx;
  color: #8c8c8c;
}

.task-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #fff;
  margin: 0 0 8rpx 0;
  line-height: 1.3;
}

.empty-hint {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  line-height: 1.4;
}

// 分页样式
.pagination-section {
  padding: 16rpx 0;
  background: transparent;
  flex-shrink: 0;
}

// 自定义分页样式
:deep(.modern-pagination) {
  .wd-pagination {
    background: rgba(255, 255, 255, 0.9) !important;
    border-radius: 16rpx !important;
    padding: 16rpx !important;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08) !important;
  }
}

/* 分页样式 - 小程序兼容写法 */
.modern-pagination .wd-pagination {
  background: rgba(255, 255, 255, 0.9) !important;
  border-radius: 16rpx !important;
  padding: 16rpx !important;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08) !important;
}

/* 全局分页样式覆盖 */
.wd-pagination {
  background: rgba(255, 255, 255, 0.9) !important;
  border-radius: 16rpx !important;
  padding: 16rpx !important;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08) !important;
}

// 动画效果
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.task-item {
  animation: slideIn 0.3s ease-out;
}

// 响应式设计
@media (max-width: 750rpx) {
  .polling-container {
    padding: 16rpx;
  }

  .task-item {
    padding: 20rpx;
  }

  .task-title {
    font-size: 26rpx;
  }

  .task-status {
    font-size: 20rpx;
    padding: 2rpx 8rpx;
  }

  .task-time {
    font-size: 20rpx;
  }
}
</style>
