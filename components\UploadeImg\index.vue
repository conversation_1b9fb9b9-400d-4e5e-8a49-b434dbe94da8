<template>
  <canvas class="canvas" :canvas-id="ID" :id="ID" :style="canvasStyle"> </canvas>
  <wd-toast />

  <view class="wrap">
    <view class="upload-img">
      <view v-if="!hidelist" class="flex">
        <template v-for="(url, index) in images" :key="url">
          <view class="upload-img__box" :style="{ width: width + 'rpx', height: width + 'rpx' }">
            <image class="_img all" @click="previewImage(url)" :src="imageBaseUrl + url" />
            <wd-icon @click="removeImg(index)" name="close-circle" custom-class="_icon" size="20px"></wd-icon>
          </view>
        </template>
      </view>
      <view v-if="images.length < maxlength" @click="uploadImg">
        <slot>
          <view class="upload-img__btn" :style="{ width: width + 'rpx', height: width + 'rpx' }">
            <image style="width: 30%" src="/static/img/camera.png" mode="widthFix" />
          </view>
        </slot>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useToast } from 'wot-design-uni'
import { cache } from '@/utils/cache'

import { getCurrentInstance } from 'vue'

const emit = defineEmits(['update:modelValue', 'success'])
const ID = 'mycanvas' + Math.random().toString(36).slice(2)

const props = defineProps({
  modelValue: { type: String, default: null }, //双向绑定
  width: { type: Number, default: 120 }, // 宽高
  url: { type: String, default: '' }, // 上传地址
  quality: { type: Number, default: 0.32 }, // 压缩质量
  header: { type: Object, default: () => ({}) }, //请求头
  maxlength: { type: Number, default: 4 }, // 上传数量
  hidelist: { type: Boolean, default: false }, // 是否显示列表
  imageBaseUrl: { type: String, default: '' }, // 图片前缀
  watermark: { type: Array, default: () => [] }, //水印
  handleImg: Function, // 处理图片
  maxCanvasSize: { type: Number, default: 1200 } // 最大canvas尺寸，防止内存溢出
})
const instance = getCurrentInstance()

const images = ref([])
const toast = useToast()
const canvasStyle = ref({
  width: '200px',
  height: '200px',
  position: 'fixed',
  top: '-9999px',
  left: '-9999px',
  zIndex: 9999
})

watch(
  () => props.modelValue,
  (val) => {
    if (val && val !== '') {
      // 过滤空字符串，避免空元素
      images.value = val.split(',').filter((url) => url.trim() !== '')
    } else {
      images.value = []
    }
  },
  { immediate: true }
)

// 验证上传URL
function validateUploadUrl() {
  if (!props.url || props.url.trim() === '') {
    toast.error('请配置上传地址')
    return false
  }
  return true
}

// 计算合适的canvas尺寸，避免内存溢出
function calculateCanvasSize(originalWidth, originalHeight) {
  const maxSize = props.maxCanvasSize

  // 如果原图尺寸都小于最大限制，直接使用原图尺寸
  if (originalWidth <= maxSize && originalHeight <= maxSize) {
    return { width: originalWidth, height: originalHeight }
  }

  // 计算缩放比例，保持宽高比
  const ratio = Math.min(maxSize / originalWidth, maxSize / originalHeight)
  const newWidth = Math.floor(originalWidth * ratio)
  const newHeight = Math.floor(originalHeight * ratio)

  return { width: newWidth, height: newHeight }
}

// 更新canvas样式
function updateCanvasStyle(width, height) {
  canvasStyle.value = {
    width: width + 'px',
    height: height + 'px',
    position: 'fixed',
    top: '-9999px',
    left: '-9999px',
    zIndex: 9999
  }
}

// 上传文件
async function uploadImg() {
  // 验证上传地址
  if (!validateUploadUrl()) {
    return
  }

  // 修复逻辑判断：只要不是隐藏列表模式，就需要handleImg函数
  if (!props.hidelist && !props.handleImg) {
    return toast.warning('请先配置上传图片处理函数: handleImg')
  }

  // 检查是否已达到最大上传数量
  if (images.value.length >= props.maxlength) {
    return toast.warning(`最多只能上传${props.maxlength}张图片`)
  }

  try {
    // 先让用户选择图片来源
    const actionSheetRes = await uni.showActionSheet({
      itemList: ['从相册选择', '拍照']
    })

    let sourceType = []
    let isCamera = false

    if (actionSheetRes.tapIndex === 0) {
      sourceType = ['album']
    } else if (actionSheetRes.tapIndex === 1) {
      sourceType = ['camera']
      isCamera = true
    }

    const chooseImageRes = await uni.chooseImage({
      count: 1,
      sourceType: sourceType
    })

    if (!chooseImageRes.tempFilePaths || chooseImageRes.tempFilePaths.length === 0) {
      return toast.warning('未选择图片')
    }

    // 只有相机拍摄时才保存到相册
    if (isCamera && chooseImageRes.tempFilePaths && chooseImageRes.tempFilePaths.length > 0) {
      try {
        await uni.saveImageToPhotosAlbum({
          filePath: chooseImageRes.tempFilePaths[0]
        })
        toast.success('照片已保存到相册')
      } catch (saveError) {
        // 保存失败可能是权限问题，但不影响上传流程
        console.log('保存到相册失败:', saveError)
        if (saveError.errMsg && saveError.errMsg.includes('auth')) {
          toast.error('需要相册权限才能保存图片')
        }
      }
    }

    toast.loading('正在上传...')

    const { width, height, path } = await uni.getImageInfo({
      src: chooseImageRes.tempFilePaths[0]
    })

    // 验证图片尺寸，防止过大的图片导致内存问题
    if (width > 4000 || height > 4000) {
      toast.close()
      return toast.warning('图片尺寸过大，请选择较小的图片（建议不超过4000x4000像素）')
    }

    // 计算合适的canvas尺寸
    const canvasSize = calculateCanvasSize(width, height)
    updateCanvasStyle(canvasSize.width, canvasSize.height)

    console.log(`原图尺寸: ${width}x${height}, Canvas尺寸: ${canvasSize.width}x${canvasSize.height}`)

    // 等待DOM更新
    await new Promise((resolve) => setTimeout(resolve, 100))

    // 创建canvas上下文，添加错误处理
    let ctx
    try {
      ctx = uni.createCanvasContext(ID, instance)
    } catch (canvasError) {
      console.error('Canvas创建失败:', canvasError)
      toast.close()
      return toast.error('图片处理失败')
    }

    // 使用计算后的尺寸绘制图片
    ctx.drawImage(path, 0, 0, canvasSize.width, canvasSize.height)
    ctx.draw(false, async () => {
      try {
        const { tempFilePath } = await uni.canvasToTempFilePath(
          {
            width: canvasSize.width,
            height: canvasSize.height,
            fileType: 'jpg',
            canvasId: ID,
            quality: props.quality
          },
          instance
        )

        const token = cache.get('token')
        const header = {
          Authorization: token ? `Bearer ${token}` : '',
          ...props.header
        }

        const uploadResult = await uni.uploadFile({
          url: props.url,
          filePath: tempFilePath,
          header,
          name: 'file'
        })

        // 修复JSON解析错误处理
        let res
        try {
          res = JSON.parse(uploadResult.data)
        } catch (parseError) {
          console.error('JSON解析失败:', parseError, '原始数据:', uploadResult.data)
          throw new Error('服务器返回数据格式错误')
        }

        if (props.handleImg) {
          const imgUrl = props.handleImg(res)
          if (imgUrl) {
            images.value.push(imgUrl)
            emit('update:modelValue', images.value.join(','))
          }
        }

        emit('success', res)
        toast.close()
        toast.success('上传成功')
      } catch (uploadError) {
        console.error('上传过程出错:', uploadError)
        toast.close()
        toast.error(uploadError.message || '上传失败')
      } finally {
        // 清理canvas资源，重置canvas尺寸
        try {
          ctx.clearRect(0, 0, canvasSize.width, canvasSize.height)
          updateCanvasStyle(200, 200) // 重置为默认小尺寸
        } catch (cleanupError) {
          console.warn('Canvas清理失败:', cleanupError)
        }
      }
    })
  } catch (error) {
    console.error('选择图片或处理过程出错:', error)
    toast.close()

    if (error.errMsg && error.errMsg.includes('cancel')) {
      // 用户取消选择，不显示错误提示
      return
    }

    toast.error(error.message || '操作失败')
  }
}

function removeImg(index) {
  if (index >= 0 && index < images.value.length) {
    images.value.splice(index, 1)
    emit('update:modelValue', images.value.join(','))
    toast.success('删除成功')
  }
}

function previewImage(url) {
  try {
    const imageUrls = images.value.map((imgUrl) => props.imageBaseUrl + imgUrl)
    const currentUrl = props.imageBaseUrl + url

    uni.previewImage({
      urls: imageUrls,
      current: currentUrl,
      fail: (error) => {
        console.error('预览图片失败:', error)
        toast.error('预览失败')
      }
    })
  } catch (error) {
    console.error('预览图片出错:', error)
    toast.error('预览失败')
  }
}
</script>

<style lang="less" scoped>
.wrap {
  display: inline-block;
}
.upload-img {
  display: flex;
  align-items: center;
  justify-content: center;
  &__box {
    margin: 8rpx;
    border: 1rpx solid #e5e5e5;
    border-radius: 12rpx;
    position: relative;
    overflow: hidden;
    ._icon {
      position: absolute;
      right: -6rpx;
      top: -6rpx;
    }
  }
  &__btn {
    background-color: #f5f5f5;
    border: 1rpx solid #e5e5e5;
    border-radius: 12rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.canvas {
  /* 动态设置尺寸，通过 canvasStyle 控制 */
  /* 默认隐藏在屏幕外，避免影响页面布局 */
  opacity: 0;
  pointer-events: none;
}
</style>
