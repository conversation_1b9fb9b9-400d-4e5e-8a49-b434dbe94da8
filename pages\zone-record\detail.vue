<template>
  <div class="material-detail">
    <!-- 头部概览卡片 -->
    <div class="header-section">
      <div class="overview-card" v-if="detail">
        <div class="overview-header">
          <div class="zone-info">
            <div class="zone-icon">
              <wd-icon name="a-controlplatform" size="24px" color="#fff"></wd-icon>
            </div>
            <div class="zone-details">
              <div class="zone-title">{{ detail.xqmc || '小区名称' }}</div>
              <div class="zone-subtitle">{{ detail.xqbm || '小区编码' }}</div>
            </div>
          </div>
          <div @click="openNavigation" class="navigation-button mar-R16" :class="{ loading: navigationLoading, disabled: !zoneDetail?.Center_Point }">
            <div class="nav-icon-wrapper">
              <wd-icon v-if="!navigationLoading" name="location" size="24px" color="#fff"></wd-icon>
              <div v-else class="loading-spinner"></div>
            </div>
          </div>

          <button style="padding: 0" @click="handlepumpHouseOpen">
            <div class="home-icon-wrapper" :class="{ 'icon-wrapper-disabled': !pumpHouseData?.length }">
              <wd-icon name="home" size="24px" color="#fff"></wd-icon>
            </div>
          </button>

          <button style="padding: 0" class="mar-L16" open-type="share">
            <div class="share-icon-wrapper">
              <wd-icon name="share" size="24px" color="#fff"></wd-icon>
            </div>
          </button>
        </div>

        <!-- 关键指标 -->
        <div class="key-metrics">
          <div class="metric-item">
            <div class="metric-icon">
              <wd-icon name="home1" size="16px" color="#1890ff"></wd-icon>
            </div>
            <div class="metric-content">
              <div class="metric-label">楼栋数</div>
              <div class="metric-value">{{ getBlockCount(detail.block_number) }}</div>
            </div>
          </div>

          <div class="metric-item">
            <div class="metric-icon">
              <wd-icon name="user-circle" size="16px" color="#52c41a"></wd-icon>
            </div>
            <div class="metric-content">
              <div class="metric-label">户数</div>
              <div class="metric-value">{{ detail.usercount || '--' }}</div>
            </div>
          </div>

          <div class="metric-item">
            <div class="metric-icon">
              <wd-icon name="cloud" size="16px" color="#13c2c2"></wd-icon>
            </div>
            <div class="metric-content">
              <div class="metric-label">进水路数</div>
              <div class="metric-value">{{ detail.jsgl?.length || '--' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 基本信息卡片 -->
    <div class="content-section">
      <CommunityIntro @delivery="(v) => (zoneDetail = v)" :detail="detail" />
      <div class="info-card basic-info" v-if="detail">
        <div class="card-header">
          <div class="header-icon">
            <wd-icon name="info-circle" size="18px" color="#4d63e0"></wd-icon>
          </div>
          <div class="header-title">基本信息</div>
        </div>

        <div class="info-grid">
          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="home1" size="14px" color="#1890ff"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">组织机构</div>
              <div class="item-value">{{ detail.fgs || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="user" size="14px" color="#52c41a"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">管理单位</div>
              <div class="item-value">{{ detail.deptname || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="app" size="14px" color="#722ed1"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">小区类别</div>
              <div class="item-value category-tag" :class="getCategoryClass(detail.xqlb)">{{ detail.xqlb || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="location" size="14px" color="#fa8c16"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">所属街道</div>
              <div class="item-value">{{ detail.ssjd || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="home" size="14px" color="#13c2c2"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">所属社区</div>
              <div class="item-value">{{ detail.communityname || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="location" size="14px" color="#eb2f96"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">行政区</div>
              <div class="item-value">{{ detail.districtname || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="setting" size="14px" color="#fa541c"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">楼宇属性</div>
              <div class="item-value">{{ detail.lcjds || '--' }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 小区基本信息 -->
      <div class="info-card community-info" v-if="detail">
        <div class="card-header">
          <div class="header-icon">
            <wd-icon name="home" size="18px" color="#52c41a"></wd-icon>
          </div>
          <div class="header-title">小区基本信息</div>
        </div>

        <div class="info-grid">
          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="setting" size="14px" color="#722ed1"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">性质</div>
              <div class="item-value">{{ detail.nature || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="app" size="14px" color="#1890ff"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">类型</div>
              <div class="item-value">{{ detail.home_type || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="home1" size="14px" color="#fa8c16"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">栋数</div>
              <div class="item-value">{{ handlerCount(detail.block_number) }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="cloud" size="14px" color="#13c2c2"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">水厂</div>
              <div class="item-value">{{ detail.water_works || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="tools" size="14px" color="#eb2f96"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">是否有泵房</div>
              <div class="item-value pump-status" :class="getPumpStatusClass(detail.is_pump_room)">{{ detail.is_pump_room || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="arrow-up" size="14px" color="#52c41a"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">黄海高程</div>
              <div class="item-value">{{ detail.yellow_sea_elevation || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="chart" size="14px" color="#fa541c"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">抄表情况</div>
              <div class="item-value">{{ detail.czccbqk || '--' }}</div>
            </div>
          </div>
        </div>

        <!-- 楼栋详情 -->
        <div class="block-details">
          <div class="block-header">
            <wd-icon name="home1" size="16px" color="#4d63e0"></wd-icon>
            <span class="block-title">楼栋详情</span>
            <span class="block-subtitle">共 {{ getBlockCount(detail.block_number) }} 栋</span>
          </div>

          <!-- 柱形图展示 -->
          <div class="block-chart gradient-bg border-primary rounded-lg">
            <!-- Y轴标签 -->
            <div class="chart-y-axis">
              <div class="y-axis-label text-secondary font-medium">栋数</div>
            </div>

            <div class="chart-container">
              <div class="chart-bar flex-column" v-for="(item, index) in getBlockChartData(detail.block_number)" :key="index">
                <div class="bar-container flex-center">
                  <div class="bar-fill" :style="{ height: item.percentage + '%' }">
                    <div class="bar-value text-primary font-semibold">{{ item.count }}</div>
                  </div>
                </div>
                <div class="bar-label text-center">
                  <div class="bar-floor text-secondary font-medium">{{ item.floor }}层</div>
                </div>
              </div>
            </div>

            <!-- X轴标签 -->
            <div class="chart-x-axis text-center">
              <div class="x-axis-label text-secondary font-medium">楼层数</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 供水系统基本信息 -->
      <div class="info-card water-system-info" v-if="detail">
        <div class="card-header">
          <div class="header-icon">
            <wd-icon name="cloud" size="18px" color="#13c2c2"></wd-icon>
          </div>
          <div class="header-title">供水系统基本信息</div>
        </div>

        <!-- 进水路数详情 -->
        <div class="water-routes-section" v-if="detail.jsgl && detail.jsgl.length">
          <div class="routes-header" @click="toggleRoutesExpanded">
            <div class="routes-header-icon">
              <wd-icon name="fork" size="18px" color="#13c2c2"></wd-icon>
            </div>
            <div class="routes-header-content">
              <div class="routes-title">进水路数详情</div>
              <div class="routes-subtitle">共 {{ detail.jsgl.length }} 路进水</div>
            </div>
            <div class="routes-toggle">
              <wd-icon :name="routesExpanded ? 'chevron-up' : 'chevron-right'" size="16px" color="#13c2c2" :class="{ rotate: routesExpanded }"></wd-icon>
            </div>
          </div>

          <div class="routes-grid" v-if="routesExpanded">
            <template v-for="(route, index) in detail.jsgl" :key="index">
              <div class="route-item">
                <div class="route-header" @click="toggleRouteDetails(index)">
                  <div class="route-badge">{{ index + 1 }}</div>
                  <div class="route-name">{{ route.name }}</div>
                  <div class="route-toggle">
                    <wd-icon :name="routeDetailsExpanded[index] ? 'chevron-up' : 'chevron-right'" size="14px" color="#13c2c2" :class="{ rotate: routeDetailsExpanded[index] }"></wd-icon>
                  </div>
                </div>
                <div class="route-details" v-if="routeDetailsExpanded[index]">
                  <template v-for="(i3, code) in Object.entries(route)" :key="code">
                    <div class="route-detail-item" v-if="i3[1] && i3[0] != 'name'">
                      <div class="detail-row">
                        <span class="detail-label">{{ keys[i3[0]] || i3[0] }}</span>
                        <span class="detail-value">{{ i3[1] }}</span>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
            </template>
          </div>
        </div>

        <div class="info-grid">
          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="cloud" size="14px" color="#13c2c2"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">小区供水方式</div>
              <div class="item-value">{{ detail.gsfs || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="setting" size="14px" color="#1890ff"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">利用市政压力</div>
              <div class="item-value">{{ detail.lyszyl || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="arrow-up" size="14px" color="#52c41a"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">加压方式</div>
              <div class="item-value">{{ detail.pressurized_zone || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="link" size="14px" color="#fa8c16"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">管网生消分离情况</div>
              <div class="item-value">{{ detail.gwsxflqk || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="app" size="14px" color="#722ed1"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">加压分区</div>
              <div class="item-value">{{ detail.jyfq || '--' }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 供水管网基本信息 -->
      <div class="info-card pipeline-info" v-if="detail">
        <div class="card-header">
          <div class="header-icon">
            <wd-icon name="link" size="18px" color="#fa8c16"></wd-icon>
          </div>
          <div class="header-title">供水管网基本信息</div>
        </div>

        <div class="info-grid">
          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="time" size="14px" color="#722ed1"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">初始建设年代</div>
              <div class="item-value">{{ detail.csjsnd || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="check-circle" size="14px" color="#52c41a"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">是否提标改造</div>
              <div class="item-value renovation-status" :class="getRenovationClass(detail.sftbgz)">{{ handlerBoolean(detail.sftbgz) }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="time" size="14px" color="#1890ff"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">改造完成年代</div>
              <div class="item-value">{{ detail.gzwcnd ? detail.gzwcnd.slice(0, 10) : '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="star" size="14px" color="#fa541c"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">优饮批次</div>
              <div class="item-value">{{ detail.excellent_drink_batch || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="link" size="14px" color="#13c2c2"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">改造前埋地管材</div>
              <div class="item-value">{{ handlerlist(detail.mdgc) || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="link" size="14px" color="#52c41a"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">改造后埋地管材</div>
              <div class="item-value">{{ handlerlist(detail.buried_pipes_after) || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="arrow-up" size="14px" color="#eb2f96"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">改造前爬墙管材</div>
              <div class="item-value">{{ handlerlist(detail.pqggc) || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="arrow-up" size="14px" color="#52c41a"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">改造后爬墙管材</div>
              <div class="item-value">{{ handlerlist(detail.wall_climbing_pipe_after) || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="barcode" size="14px" color="#fa8c16"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">常用总表口径(mm)</div>
              <div class="item-value">{{ detail.commonly_used_general_table_caliber || '--' }}</div>
            </div>
          </div>
        </div>
      </div>
      <!-- 二次供水基本信息 -->
      <div class="info-card secondary-water-info" v-if="detail">
        <div class="card-header">
          <div class="header-icon">
            <wd-icon name="tools" size="18px" color="#722ed1"></wd-icon>
          </div>
          <div class="header-title">二次供水基本信息</div>
        </div>

        <div class="info-grid">
          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="setting" size="14px" color="#722ed1"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">泵房状态</div>
              <div class="item-value pump-room-status" :class="getPumpRoomStatusClass(detail.pump_room_status)">{{ detail.pump_room_status || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="user" size="14px" color="#1890ff"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">泵房是否由我司运维</div>
              <div class="item-value maintenance-status" :class="getMaintenanceClass(detail.is_pump_room_our_company)">{{ handlerBoolean(detail.is_pump_room_our_company) }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 泵房改造前（现状）情况 -->
      <div class="info-card pump-before-info" v-if="detail && isShow.includes(1)">
        <div class="card-header">
          <div class="header-icon">
            <wd-icon name="tools" size="18px" color="#eb2f96"></wd-icon>
          </div>
          <div class="header-title">泵房改造前（现状）情况</div>
        </div>

        <div class="info-grid">
          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="home" size="14px" color="#eb2f96"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">泵房数量（座）</div>
              <div class="item-value">{{ detail.before_pump_room_count || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="edit" size="14px" color="#722ed1"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">备注</div>
              <div class="item-value">{{ detail.before_remark || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="barcode" size="14px" color="#1890ff"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">设计加压规模(m³/日)</div>
              <div class="item-value">{{ detail.before_pressure_scale || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="user-circle" size="14px" color="#52c41a"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">加压户数</div>
              <div class="item-value">{{ detail.before_number_pressurized || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="cloud" size="14px" color="#13c2c2"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">供水方式</div>
              <div class="item-value">{{ detail.before_water_supply_method || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="app" size="14px" color="#fa8c16"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">加压分区情况</div>
              <div class="item-value">{{ detail.before_pressurized_partition_situation || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="tools" size="14px" color="#fa541c"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">水泵数量(台)</div>
              <div class="item-value">{{ detail.before_number_pumps || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="setting" size="14px" color="#722ed1"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">水泵材质</div>
              <div class="item-value">{{ detail.before_pump_material || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="cloud" size="14px" color="#1890ff"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">水池/箱(座)</div>
              <div class="item-value">{{ detail.before_pool_number || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="setting" size="14px" color="#52c41a"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">水池/箱材质</div>
              <div class="item-value">{{ detail.before_pool_material || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="link" size="14px" color="#13c2c2"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">泵房管路材质</div>
              <div class="item-value">{{ detail.before_pump_room_piping_material || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="tools" size="14px" color="#fa8c16"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">泵房阀门主要材质</div>
              <div class="item-value">{{ detail.before_pump_room_valve_main_material || '--' }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 泵房临供情况 -->
      <div class="info-card pump-temp-info" v-if="detail && isShow.includes(2)">
        <div class="card-header">
          <div class="header-icon">
            <wd-icon name="time" size="18px" color="#fa8c16"></wd-icon>
          </div>
          <div class="header-title">泵房临供情况</div>
        </div>

        <div class="info-grid">
          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="link" size="14px" color="#13c2c2"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">泵房管路材质</div>
              <div class="item-value">{{ detail.lgbf_pump_room_piping_material || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="tools" size="14px" color="#fa8c16"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">泵房阀门主要材质</div>
              <div class="item-value">{{ detail.lgbf_pump_room_valve_main_material || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="time" size="14px" color="#722ed1"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">临时供水开始时间</div>
              <div class="item-value">{{ detail.egcsjsnd || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="cloud" size="14px" color="#1890ff"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">供水方式</div>
              <div class="item-value">{{ detail.lgbf_water_supply_method || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="edit" size="14px" color="#52c41a"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">备注</div>
              <div class="item-value">{{ detail.lgbf_remark || '--' }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 泵房改造后情况 -->
      <div class="info-card pump-after-info" v-if="detail && isShow.includes(3)">
        <div class="card-header">
          <div class="header-icon">
            <wd-icon name="check-circle" size="18px" color="#52c41a"></wd-icon>
          </div>
          <div class="header-title">泵房改造后情况</div>
        </div>

        <div class="info-grid">
          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="home" size="14px" color="#52c41a"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">泵房数量（座）</div>
              <div class="item-value">{{ detail.bfsl || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="edit" size="14px" color="#722ed1"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">备注</div>
              <div class="item-value">{{ detail.eg_remark || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="barcode" size="14px" color="#1890ff"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">设计加压规模(m³/日)</div>
              <div class="item-value">{{ detail.jygm || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="user-circle" size="14px" color="#fa8c16"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">加压户数</div>
              <div class="item-value">{{ detail.jyhs || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="cloud" size="14px" color="#13c2c2"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">供水方式</div>
              <div class="item-value">{{ detail.water_supply_method || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="app" size="14px" color="#eb2f96"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">加压分区情况</div>
              <div class="item-value">{{ detail.pressurized_partition_situation || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="tools" size="14px" color="#fa541c"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">水泵数量(台)</div>
              <div class="item-value">{{ detail.sbsl || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="setting" size="14px" color="#722ed1"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">水泵材质</div>
              <div class="item-value">{{ detail.sbcz || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="cloud" size="14px" color="#1890ff"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">水池/箱(座)</div>
              <div class="item-value">{{ detail.dxsc || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="setting" size="14px" color="#52c41a"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">水池/箱材质</div>
              <div class="item-value">{{ detail.dxscxcz || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="link" size="14px" color="#13c2c2"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">泵房管路材质</div>
              <div class="item-value">{{ detail.bfglcz || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="tools" size="14px" color="#fa8c16"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">泵房阀门主要材质</div>
              <div class="item-value">{{ detail.bffmzycz || '--' }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="item-icon">
              <wd-icon name="time" size="14px" color="#52c41a"></wd-icon>
            </div>
            <div class="item-content">
              <div class="item-label">改造完成年代</div>
              <div class="item-value">{{ detail.eggzwcnd || '--' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <WaterTankArchives :data="waterTankArchivesData" :detail="detail" @createdSuccess="getDetailData(xqbmID)" />
    <FeedbackPopup ref="feedbackRef" :id="FeedbackID" />

    <FloatingButton :draggable="true" :size="80" icon="move" background-color="#33aa71" :initial-position="{ x: 300, y: 300 }" :bounds="{ top: 100, right: 50, bottom: 100, left: 50 }" @click="handleMoveClick">
      <wd-icon name="chat" size="24px" color="#ffffff"></wd-icon>
    </FloatingButton>
  </div>

  <wd-popup v-model="pumpHouseOpen" closable position="bottom" custom-class="u2-popup" custom-style="height: 400px;z-index:999;" @close="handleClosePumpHouse">
    <div class="pump-house-popup">
      <div class="popup-header">
        <div class="header-title">
          <wd-icon name="home" size="20px" color="#13c2c2" />
          <span>选择泵房</span>
        </div>
        <div class="header-count">共 {{ pumpHouseData?.length || 0 }} 个泵房</div>
      </div>

      <div class="pump-house-list">
        <div v-for="(item, index) in pumpHouseData" :key="index" class="pump-house-item" @click="handleSelectPumpHouse(item)">
          <div class="item-icon">
            <wd-icon name="home" size="16px" color="#13c2c2" />
          </div>
          <div class="item-content">
            <div class="item-title">{{ item.PumpHouseName || `泵房 #${index + 1}` }}</div>
            <div class="item-status">
              <span class="status-badge" :class="getProgressStatusClass(item.ProgressStatus)">
                {{ item.ProgressStatus || '-' }}
              </span>
              <span class="status-badge" :class="getRemoldStateClass(item.RemouldState)">
                {{ item.RemouldState || '-' }}
              </span>
            </div>
          </div>
          <div class="item-arrow">
            <wd-icon name="arrow-right" size="14px" color="#999" />
          </div>
        </div>

        <div v-if="!pumpHouseData || pumpHouseData.length === 0" class="empty-state">
          <div class="empty-icon">
            <wd-icon name="home" size="48px" color="#ccc" />
          </div>
          <div class="empty-text">暂无泵房数据</div>
        </div>
      </div>
    </div>
  </wd-popup>
  <wd-message-box />
  <wd-toast />
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'
import { onLoad, onShow, onShareAppMessage } from '@dcloudio/uni-app'
import { getFuzzyDetail } from '@/services/model/zone.record'
import FloatingButton from '@/components/FloatingButton/index.vue'
import { feedbackIssueCreateRecordApi } from '@/services/model/feedback.issue.js'
import * as CommonApi from '@/services/model/common.js'
import FeedbackPopup from '@/components/FeedbackPopup/index.vue'
import { useToast, useMessage } from 'wot-design-uni'
import CommunityIntro from './components/CommunityIntro/index.vue'
import WaterTankArchives from './components/WaterTankArchives/index.vue'

const detail = ref({})
const zoneDetail = ref({})
const toast = useToast()
const message = useMessage()
const pumpHouseOpen = ref(false)

const keys = { pipeDia: '市政管径', location: '水表组位置', pipe: '市政管材', status: '水表组阀门状态', waterStatus: '预留口阀门状态', value: '预留阀门口号', waterLocation: '预留口位置' }

// 检测是否为微信小程序环境
const isWechatMiniProgram = ref(false)

// 导航加载状态
const navigationLoading = ref(false)

// 折叠状态管理 - 使用数组结构提高微信小程序兼容性
const routesExpanded = ref(false)
const routeDetailsExpanded = ref([])
const feedbackRef = ref(null)

const xqbmID = ref(null)
let openPopup = false

onLoad(async (options) => {
  if (options.open == '1') openPopup = true
  // 检测运行环境
  try {
    // 检测是否为微信小程序环境
    isWechatMiniProgram.value = typeof wx !== 'undefined' && wx.getSystemInfoSync
  } catch (e) {
    isWechatMiniProgram.value = false
  }
  const { xqbm } = options
  xqbmID.value = xqbm
})

onShow(async () => {
  if (xqbmID.value) {
    await getDetailData(xqbmID.value)
    if (openPopup) {
      handleMoveClick()
      openPopup = false
    }
  }
})
const waterTankArchivesData = ref(null)
const pumpHouseData = ref(null)
// 获取详情数据
async function getDetailData(xqbm) {
  const { data, waterTankArchives, pumpHouseList } = await getFuzzyDetail(xqbm)
  waterTankArchivesData.value = waterTankArchives
  pumpHouseData.value = pumpHouseList
  // 处理进水路数数据
  if (data.jsgl == '[]' || !data.jsgl || data.jsgl?.length < 10) {
    data.jsgl = []
  } else {
    data.jsgl = JSON.parse(data.jsgl)
  }

  // 初始化折叠状态数组 - 所有进水路数默认收起
  if (data.jsgl && data.jsgl.length > 0) {
    routeDetailsExpanded.value = new Array(data.jsgl.length).fill(false)
  }

  uni.setNavigationBarTitle({ title: data.xqmc })
  detail.value = data
}

async function openNavigation() {
  uni.vibrateShort({ type: 'medium' })
  try {
    // 检查是否有坐标信息
    if (!zoneDetail.value?.Center_Point) {
      return toast.warning('暂无坐标信息')
    }

    // 防止重复点击
    if (navigationLoading.value) {
      return
    }

    // 设置加载状态
    navigationLoading.value = true

    // 显示加载提示
    uni.showLoading({
      title: '正在获取位置...',
      mask: true
    })

    const [originalLng, originalLat] = zoneDetail.value.Center_Point.split(',')

    // 验证坐标有效性
    if (isNaN(originalLng) || isNaN(originalLat)) {
      uni.hideLoading()
      return toast.error('坐标信息格式错误')
    }

    // 转换成高德坐标（GCJ02坐标系）
    const { data } = await CommonApi.coordinateTransformation({ lng: originalLng, lat: originalLat, fn: 'wgs2gcj' })

    // 计算转换后的坐标
    const lng = Number(originalLng) + Number(data.lng.replace(originalLng, ''))
    const lat = Number(originalLat) + Number(data.lat.replace(originalLat, ''))

    uni.hideLoading()

    // 检测是否为微信小程序环境
    if (isWechatMiniProgram.value) {
      // 使用微信小程序原生导航API
      wx.openLocation({
        latitude: lat,
        longitude: lng,
        scale: 16, // 地图缩放级别，范围5-18
        name: detail.value.xqmc || '目标位置',
        address: `${detail.value.ssjd || ''}${detail.value.communityname || ''}${detail.value.xqmc || ''}`,
        success: () => {
          navigationLoading.value = false
        },
        fail: (error) => {
          console.error('导航失败:', error)
          toast.error('导航失败，请稍后重试')
          navigationLoading.value = false
        }
      })
    } else {
      // 非微信小程序环境，使用uni-app的openLocation
      uni.openLocation({
        latitude: lat,
        longitude: lng,
        scale: 16,
        name: detail.value.xqmc || '目标位置',
        address: `${detail.value.ssjd || ''}${detail.value.communityname || ''}${detail.value.xqmc || ''}`,
        success: () => {
          navigationLoading.value = false
        },
        fail: (error) => {
          console.error('导航失败:', error)
          toast.error('导航失败，请稍后重试')
          navigationLoading.value = false
        }
      })
    }
  } catch (error) {
    uni.hideLoading()
    navigationLoading.value = false
    console.error('openNavigation 执行失败:', error)

    // 根据错误类型给出不同的提示
    if (error.message && error.message.includes('网络')) {
      toast.error('网络连接失败，请检查网络后重试')
    } else if (error.message && error.message.includes('坐标转换')) {
      toast.error('坐标转换失败，请稍后重试')
    } else {
      toast.error('导航功能暂时不可用，请稍后重试')
    }
  }
}

onShareAppMessage(() => {
  uni.vibrateShort({ type: 'medium' })
  return {
    title: `档案详情 - ${detail.value.xqmc}`,
    path: `/pages/zone-record/detail?xqbm=${detail.value.xqbm}`
  }
})

const FeedbackID = ref(null)
async function handleMoveClick() {
  try {
    // 显示加载提示
    uni.showLoading({
      title: '处理中...',
      mask: true
    })

    const { data } = await feedbackIssueCreateRecordApi({
      FeedbackContent: `${detail.value.xqmc}`,
      FileCode: detail.value.xqbm
    })
    FeedbackID.value = data.FeedbackID

    const isShow = feedbackRef.value.getFeedbackIsOpen()
    if (isShow) {
      feedbackRef.value.close()
    } else {
      feedbackRef.value.getFeedbackDetail(data.FeedbackID)
    }
  } catch (error) {
    console.error('页面: handleMoveClick 执行失败', error)
    uni.showToast({
      title: '操作失败',
      icon: 'error'
    })
  } finally {
    uni.hideLoading()
  }
}

// 计算显示的泵房信息
const isShow = computed(() => {
  if (detail.value.is_pump_room && detail.value.is_pump_room.includes('否')) return []

  switch (detail.value.pump_room_status ?? '#') {
    case '无需二供改造':
      return [1]
    case '查漏补缺':
      return [1]
    case '已立项未进场':
      return [1]
    case '已立项施工中(指临供状态)':
      return [1, 2]
    case '已通水':
      return [1, 2, 3]
    default:
      return []
  }
})

// 获取状态样式类
function getStatusClass(status) {
  const statusMap = {
    无需二供改造: 'status-complete',
    查漏补缺: 'status-partial',
    已立项未进场: 'status-pending',
    '已立项施工中(指临供状态)': 'status-progress',
    已通水: 'status-complete'
  }
  return statusMap[status] || 'status-unknown'
}

// 获取状态文本
function getStatusText(status) {
  const textMap = {
    无需二供改造: '无需改造',
    查漏补缺: '查漏补缺',
    已立项未进场: '待进场',
    '已立项施工中(指临供状态)': '施工中',
    已通水: '已完成'
  }
  return textMap[status] || '未知状态'
}

// 获取楼栋数量
function getBlockCount(val) {
  if (!val || val == '""') return '0'
  const list = val.split(';')
  const total = list.reduce((total, item) => {
    let match = item.match(/栋数：(\d+)栋/)
    if (match) {
      let count = parseInt(match[1], 10)
      return total + count
    } else {
      return total
    }
  }, 0)
  return total.toString()
}

// 获取小区类别样式类
function getCategoryClass(category) {
  const classMap = {
    住宅小区: 'category-residential',
    商业区: 'category-commercial',
    工业区: 'category-industrial',
    混合区: 'category-mixed'
  }
  return classMap[category] || 'category-default'
}

// 获取泵房状态样式类
function getPumpStatusClass(status) {
  if (status === '是' || status === true) {
    return 'pump-yes'
  } else if (status === '否' || status === false) {
    return 'pump-no'
  }
  return 'pump-unknown'
}

// 获取泵房状态样式类（别名）
function getPumpRoomStatusClass(status) {
  return getStatusClass(status)
}

// 获取改造状态样式类
function getRenovationClass(status) {
  if (status === true || status === '是') {
    return 'renovation-yes'
  } else if (status === false || status === '否') {
    return 'renovation-no'
  }
  return 'renovation-unknown'
}

// 获取运维状态样式类
function getMaintenanceClass(status) {
  if (status === true || status === '是') {
    return 'maintenance-yes'
  } else if (status === false || status === '否') {
    return 'maintenance-no'
  }
  return 'maintenance-unknown'
}

// 处理布尔值
function handlerBoolean(value) {
  return value === true ? '是' : value === false ? '否' : value
}

// 处理列表数据
function handlerlist(value) {
  if (!value || value == '""') return ''
  if (/^[\[]/.test(value)) {
    try {
      // 尝试解析为 JSON 数组
      return JSON.parse(value).join('、')
    } catch (error) {
      // 如果解析失败，手动处理类似 [不锈钢] 的格式
      const match = value.match(/^\[(.+)\]$/)
      if (match) {
        // 移除方括号，按逗号分割（如果有的话），然后用顿号连接
        return match[1]
          .split(',')
          .map((item) => item.trim())
          .join('、')
      }
      // 如果都不匹配，返回原值去掉引号
      return value.replace(/"/g, '')
    }
  }
  return value.replace(/"/g, '')
}

// 处理楼栋数据
function handlerCount(val) {
  if (!val || val == '""') return '暂无数据'
  const list = val.split(';')
  return (
    '楼栋总数 ' +
    list.reduce((total, item) => {
      let match = item.match(/栋数：(\d+)栋/)
      if (match) {
        let count = parseInt(match[1], 10)
        return total + count
      } else {
        return total
      }
    }, 0)
  )
}

// 处理楼栋数据为柱形图数据（层数为X轴，栋数为Y轴）
function getBlockChartData(val) {
  if (!val || val == '""') return []

  const list = val.split(';')
  const floorData = new Map() // 使用Map来统计每个层数对应的栋数

  list.forEach((item) => {
    // 清理数据，移除可能的引号和空格
    const cleanItem = item.trim().replace(/"/g, '')

    // 提取层数信息 - 处理逗号分隔的格式
    const floorMatch = cleanItem.match(/最高楼层：(\d+)/)
    const floor = floorMatch ? parseInt(floorMatch[1], 10) : 0
    // 提取栋数信息
    const countMatch = cleanItem.match(/栋数：(\d+)栋/)
    const count = countMatch ? parseInt(countMatch[1], 10) : 0

    if (floor > 0 && count > 0) {
      // 如果该层数已存在，累加栋数；否则新建
      const existingCount = floorData.get(floor) || 0
      floorData.set(floor, existingCount + count)
    }
  })

  // 转换为数组并排序
  const chartData = Array.from(floorData.entries())
    .map(([floor, count]) => ({ floor: floor, count: count, percentage: 0 }))
    .sort((a, b) => a.floor - b.floor) // 按层数排序

  // 计算百分比（基于最大栋数）
  if (chartData.length > 0) {
    const maxCount = Math.max(...chartData.map((item) => item.count))
    chartData.forEach((item) => {
      item.percentage = (item.count / maxCount) * 100
    })
  }

  return chartData
}

// 切换进水路数整体展开/收起
function toggleRoutesExpanded() {
  uni.vibrateShort({ type: 'medium' })

  routesExpanded.value = !routesExpanded.value

  // 微信小程序环境下强制更新
  if (isWechatMiniProgram.value) {
    // 使用 $forceUpdate 或重新赋值来强制更新
    nextTick(() => {
      const currentValue = routesExpanded.value
      routesExpanded.value = !currentValue
      routesExpanded.value = currentValue
    })
  }
}

// 切换单个进水路数详情展开/收起
function toggleRouteDetails(index) {
  uni.vibrateShort({ type: 'medium' })
  // 确保数组和索引存在
  if (!routeDetailsExpanded.value || index >= routeDetailsExpanded.value.length || index < 0) {
    return
  }

  // 创建新数组，确保响应式更新
  const newArray = [...routeDetailsExpanded.value]
  newArray[index] = !newArray[index]
  routeDetailsExpanded.value = newArray

  // 微信小程序环境下额外处理
  if (isWechatMiniProgram.value) {
    nextTick(() => {
      // 再次确保状态更新
      const currentArray = [...routeDetailsExpanded.value]
      routeDetailsExpanded.value = currentArray
    })
  }
}

function handlepumpHouseOpen() {
  if (pumpHouseData.value?.length == 0) return
  if (pumpHouseData.value.length == 1) {
    uni.navigateTo({ url: `/pages/pump-house/detail?pumpRoomNumber=${pumpHouseData.value[0].PumpRoomNumber}` })
  } else {
    pumpHouseOpen.value = true
  }
}

// 关闭泵房弹窗
function handleClosePumpHouse() {
  pumpHouseOpen.value = false
}

// 选择泵房
function handleSelectPumpHouse(item) {
  pumpHouseOpen.value = false
  uni.navigateTo({ url: `/pages/pump-house/detail?pumpRoomNumber=${item.PumpRoomNumber}` })
}

// 获取进度状态样式类
function getProgressStatusClass(status) {
  const statusMap = { 已完成: 'status-completed', 进行中: 'status-progress', 未开始: 'status-pending', 已暂停: 'status-paused' }
  return statusMap[status] || 'status-default'
}

// 获取改造状态样式类
function getRemoldStateClass(state) {
  const stateMap = { 已改造: 'state-completed', 改造中: 'state-progress', 未改造: 'state-pending', 无需改造: 'state-none' }
  return stateMap[state] || 'state-default'
}
</script>

<style lang="less" scoped>
.material-detail {
  background: #f5f5f5;
  min-height: 100vh;
}

// 头部区域
.header-section {
  padding: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.overview-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.zone-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.zone-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #4d63e0 0%, #6366f1 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(77, 99, 224, 0.3);
}

.zone-details {
  flex: 1;
}

.zone-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
  line-height: 1.2;
}

.zone-subtitle {
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
  line-height: 1.3;
}

.status-badge {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;

  &.status-complete {
    background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
    border: 1rpx solid #b7eb8f;

    .status-dot {
      background: #52c41a;
      box-shadow: 0 0 0 3rpx rgba(82, 196, 26, 0.2);
    }

    .status-text {
      color: #52c41a;
    }
  }

  &.status-partial {
    background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
    border: 1rpx solid #ffcc02;

    .status-dot {
      background: #fa8c16;
      box-shadow: 0 0 0 3rpx rgba(250, 140, 22, 0.2);
    }

    .status-text {
      color: #fa8c16;
    }
  }

  &.status-pending {
    background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
    border: 1rpx solid #91d5ff;

    .status-dot {
      background: #1890ff;
      box-shadow: 0 0 0 3rpx rgba(24, 144, 255, 0.2);
    }

    .status-text {
      color: #1890ff;
    }
  }

  &.status-progress {
    background: linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%);
    border: 1rpx solid #d3adf7;

    .status-dot {
      background: #722ed1;
      box-shadow: 0 0 0 3rpx rgba(114, 46, 209, 0.2);
    }

    .status-text {
      color: #722ed1;
    }
  }

  &.status-unknown {
    background: linear-gradient(135deg, #f5f5f5 0%, #d9d9d9 100%);
    border: 1rpx solid #d9d9d9;

    .status-dot {
      background: #666;
    }

    .status-text {
      color: #666;
    }
  }
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

.status-text {
  font-size: 22rpx;
  font-weight: 600;
  line-height: 1.2;
}

// 导航按钮
.navigation-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
  box-shadow: 0 8rpx 32rpx rgba(76, 175, 80, 0.3), 0 2rpx 8rpx rgba(69, 160, 73, 0.2);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-2rpx) scale(1.02);
    box-shadow: 0 12rpx 40rpx rgba(76, 175, 80, 0.4), 0 4rpx 12rpx rgba(69, 160, 73, 0.3);
    background: linear-gradient(135deg, #5cbf60 0%, #4caf50 100%);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 4rpx 20rpx rgba(76, 175, 80, 0.3), 0 1rpx 6rpx rgba(69, 160, 73, 0.2);
  }

  // 添加按压动画效果
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
  }

  &:active::before {
    width: 200rpx;
    height: 200rpx;
  }
}

.nav-icon-wrapper {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1;

  .navigation-button:hover & {
    transform: scale(1.1) rotate(5deg);
    background: rgba(255, 255, 255, 0.3);
    box-shadow: 0 6rpx 20rpx rgba(255, 255, 255, 0.2);
  }

  .navigation-button:active & {
    transform: scale(1.05) rotate(2deg);
  }
}

.nav-text {
  font-size: 20rpx;
  font-weight: 600;
  color: #fff;
  line-height: 1.2;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;

  .navigation-button:hover & {
    transform: translateY(-1rpx);
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  }

  .navigation-button:active & {
    transform: translateY(0);
  }
}

// 加载动画
.loading-spinner {
  width: 18rpx;
  height: 18rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-top: 2rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 导航按钮状态
.navigation-button.loading {
  pointer-events: none;
  opacity: 0.8;

  .nav-icon-wrapper {
    background: rgba(255, 255, 255, 0.3);
  }

  .nav-text {
    font-size: 18rpx;
  }
}

.navigation-button.disabled {
  opacity: 0.5;
  pointer-events: none;
  background: linear-gradient(135deg, #ccc 0%, #999 100%) !important;

  &:hover {
    transform: none;
    box-shadow: 0 8rpx 32rpx rgba(204, 204, 204, 0.3), 0 2rpx 8rpx rgba(153, 153, 153, 0.2);
    background: linear-gradient(135deg, #ccc 0%, #999 100%);
  }

  .nav-icon-wrapper {
    background: rgba(255, 255, 255, 0.1);

    &:hover {
      transform: none;
      background: rgba(255, 255, 255, 0.1);
    }
  }

  .nav-text {
    color: rgba(255, 255, 255, 0.7);
  }
}

// 分享按钮
.share-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1), 0 2rpx 8rpx rgba(0, 0, 0, 0.05), inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.share-icon-wrapper {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.4), 0 2rpx 8rpx rgba(118, 75, 162, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  .share-button:hover & {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.5), 0 3rpx 12rpx rgba(118, 75, 162, 0.4);
  }

  .share-button:active & {
    transform: scale(1.05) rotate(2deg);
  }
}
.home-icon-wrapper {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  background: #5965e9;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.4), 0 2rpx 8rpx rgba(118, 75, 162, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  .share-button:hover & {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.5), 0 3rpx 12rpx rgba(118, 75, 162, 0.4);
  }

  .share-button:active & {
    transform: scale(1.05) rotate(2deg);
  }
}

.share-text {
  font-size: 20rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;

  .share-button:hover & {
    color: #667eea;
    transform: translateY(-1rpx);
  }

  .share-button:active & {
    transform: translateY(0);
  }
}

// 关键指标
.key-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120rpx, 1fr));
  gap: 16rpx;
}

.metric-item {
  display: flex;
  align-items: center;
  background: rgba(77, 99, 224, 0.04);
  padding: 12rpx 16rpx;
  border-radius: 16rpx;
  border-left: 3rpx solid #4d63e0;
}

.metric-icon {
  width: 32rpx;
  height: 32rpx;
  border-radius: 8rpx;
  background: rgba(77, 99, 224, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
}

.metric-content {
  flex: 1;
}

.metric-label {
  font-size: 20rpx;
  color: #666;
  line-height: 1.2;
  margin-bottom: 2rpx;
}

.metric-value {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
}

// 内容区域
.content-section {
  padding: 20rpx;
}

.info-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 24rpx;
  margin-top: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border-left: 4rpx solid #e8e8e8;

  &.basic-info {
    border-left-color: #4d63e0;
  }

  &.community-info {
    border-left-color: #52c41a;
  }

  &.water-system-info {
    border-left-color: #13c2c2;
  }

  &.pipeline-info {
    border-left-color: #fa8c16;
  }

  &.secondary-water-info {
    border-left-color: #722ed1;
  }

  &.pump-before-info {
    border-left-color: #eb2f96;
  }

  &.pump-temp-info {
    border-left-color: #fa8c16;
  }

  &.pump-after-info {
    border-left-color: #52c41a;
  }
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.header-icon {
  width: 36rpx;
  height: 36rpx;
  border-radius: 10rpx;
  background: rgba(77, 99, 224, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
}

.header-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
}

// 信息网格
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300rpx, 1fr));
  gap: 16rpx;
}

.info-item {
  display: flex;
  align-items: center;
  background: rgba(77, 99, 224, 0.02);
  padding: 16rpx;
  border-radius: 12rpx;
  border-left: 3rpx solid #7df05a;
  transition: all 0.3s ease;
}

.item-icon {
  width: 32rpx;
  height: 32rpx;
  border-radius: 8rpx;
  background: rgba(77, 99, 224, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
}

.item-content {
  flex: 1;
}

.item-label {
  font-size: 22rpx;
  color: #666;
  line-height: 1.2;
  margin-bottom: 4rpx;
}

.item-value {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.2;

  &.category-tag {
    display: inline-block;
    padding: 4rpx 12rpx;
    border-radius: 12rpx;
    font-size: 20rpx;
    font-weight: 600;

    &.category-residential {
      background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
      color: #52c41a;
      border: 1rpx solid #b7eb8f;
    }

    &.category-commercial {
      background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
      color: #1890ff;
      border: 1rpx solid #91d5ff;
    }

    &.category-industrial {
      background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
      color: #fa8c16;
      border: 1rpx solid #ffcc02;
    }

    &.category-mixed {
      background: linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%);
      color: #722ed1;
      border: 1rpx solid #d3adf7;
    }

    &.category-default {
      background: linear-gradient(135deg, #f5f5f5 0%, #d9d9d9 100%);
      color: #666;
      border: 1rpx solid #d9d9d9;
    }
  }

  &.pump-status {
    display: inline-block;
    padding: 4rpx 12rpx;
    border-radius: 12rpx;
    font-size: 20rpx;
    font-weight: 600;

    &.pump-yes {
      background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
      color: #52c41a;
      border: 1rpx solid #b7eb8f;
    }

    &.pump-no {
      background: linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%);
      color: #ff4d4f;
      border: 1rpx solid #ffa39e;
    }

    &.pump-unknown {
      background: linear-gradient(135deg, #f5f5f5 0%, #d9d9d9 100%);
      color: #666;
      border: 1rpx solid #d9d9d9;
    }
  }
}

// 进水路数详情
.water-routes-section {
  margin-top: 24rpx;
}

.routes-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 16rpx 20rpx;
  background: linear-gradient(135deg, rgba(19, 194, 194, 0.08) 0%, rgba(19, 194, 194, 0.04) 100%);
  border-radius: 16rpx;
  border-left: 4rpx solid #13c2c2;
  cursor: pointer;
  transition: all 0.3s ease;
  &:active {
    transform: translateY(0);
  }
}

.routes-header-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #13c2c2 0%, #36cfc9 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(19, 194, 194, 0.3);
}

.routes-header-content {
  flex: 1;
}

.routes-title {
  font-size: 28rpx;
  font-weight: 700;
  color: #333;
  line-height: 1.2;
  margin-bottom: 4rpx;
}

.routes-subtitle {
  font-size: 22rpx;
  color: #13c2c2;
  font-weight: 500;
  line-height: 1.2;
}

.routes-toggle {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: rgba(19, 194, 194, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 12rpx;
  transition: all 0.3s ease;

  .rotate {
    transform: rotate(180deg);
  }

  .icon-rotate {
    transform: rotate(180deg);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

.routes-grid {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.route-item {
  background: #fff;
  border-radius: 20rpx;
  padding: 20rpx;
  border-left: 4rpx solid #13c2c2;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2rpx;
    background: linear-gradient(90deg, #13c2c2 0%, #36cfc9 100%);
  }
}

.route-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  padding: 8rpx 12rpx;
  border-bottom: 1rpx solid rgba(19, 194, 194, 0.1);
  border-radius: 12rpx;
  cursor: pointer;
  transition: all 0.2s ease;

  &:active {
    transform: translateY(0);
  }
}

.route-badge {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #13c2c2 0%, #36cfc9 100%);
  color: #fff;
  font-size: 20rpx;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(19, 194, 194, 0.3);
}

.route-name {
  font-size: 28rpx;
  font-weight: 700;
  color: #333;
  line-height: 1.2;
  flex: 1;
}

.route-toggle {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background: rgba(19, 194, 194, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8rpx;
  transition: all 0.2s ease;

  .rotate {
    transform: rotate(180deg);
  }
}

.route-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-top: 12rpx;
  padding-top: 12rpx;
  border-top: 1rpx solid rgba(19, 194, 194, 0.1);
}

.route-detail-item {
  background: rgba(19, 194, 194, 0.03);
  border-radius: 8rpx;
  padding: 12rpx 16rpx;
  border-left: 3rpx solid rgba(19, 194, 194, 0.3);
  transition: all 0.2s ease;

  &:hover {
    background: rgba(19, 194, 194, 0.06);
    transform: translateX(4rpx);
  }
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  min-height: 36rpx;
  gap: 16rpx;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
  line-height: 1.3;
  flex-shrink: 0;
  min-width: 120rpx;
  max-width: 160rpx;
}

.detail-value {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
  text-align: right;
  word-break: break-all;
  flex: 1;
}

// 楼栋详情
.block-details {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.block-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.block-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  margin-left: 8rpx;
  line-height: 1.2;
}

.block-subtitle {
  font-size: 20rpx;
  color: #4d63e0;
  font-weight: 500;
  margin-left: auto;
  background: rgba(77, 99, 224, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

// 通用样式类
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.text-center {
  text-align: center;
}

.gradient-bg {
  background: linear-gradient(135deg, rgba(77, 99, 224, 0.02) 0%, rgba(77, 99, 224, 0.01) 100%);
}

.border-primary {
  border: 1rpx solid rgba(77, 99, 224, 0.1);
}

.rounded-lg {
  border-radius: 16rpx;
}

.text-primary {
  color: #4d63e0;
}

.text-secondary {
  color: #666;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

// 柱形图样式
.block-chart {
  margin-top: 16rpx;
  position: relative;
}

.chart-y-axis {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%) rotate(-90deg);
  z-index: 2;
}

.y-axis-label {
  font-size: 20rpx;
  color: #666;
  font-weight: 500;
}

.chart-container {
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  height: 200rpx;
  padding: 20rpx 32rpx 0 24rpx;
  margin: 0 16rpx;
  position: relative;
  overflow: hidden;
}

.chart-container::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: rgba(77, 99, 224, 0.2);
}

.chart-x-axis {
  margin-top: 12rpx;
  text-align: center;
}

.x-axis-label {
  font-size: 20rpx;
  color: #666;
  font-weight: 500;
}

.chart-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  max-width: 80rpx;
  margin: 0 4rpx;
}

.bar-container {
  width: 100%;
  height: 140rpx;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  position: relative;
}

.bar-fill {
  width: 32rpx;
  min-height: 8rpx;
  background: linear-gradient(180deg, #4d63e0 0%, #6366f1 50%, #8b5cf6 100%);
  border-radius: 4rpx 4rpx 0 0;
  position: relative;
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  animation: barGrow 1.2s ease-out;
  box-shadow: 0 2rpx 8rpx rgba(77, 99, 224, 0.3);
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.bar-value {
  position: absolute;
  top: -24rpx;
  font-size: 18rpx;
  font-weight: 600;
  color: #4d63e0;
  background: rgba(255, 255, 255, 0.9);
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  border: 1rpx solid rgba(77, 99, 224, 0.2);
  white-space: nowrap;
  min-width: 24rpx;
  text-align: center;
}

.bar-label {
  margin-top: 12rpx;
  text-align: center;
  width: 100%;
}

.bar-floor {
  font-size: 20rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.2;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

@keyframes barGrow {
  from {
    height: 0;
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .key-metrics {
    grid-template-columns: 1fr;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .chart-container {
    height: 160rpx;
    padding: 16rpx 28rpx 0 20rpx;
  }

  .bar-container {
    height: 120rpx;
  }

  .bar-fill {
    width: 28rpx;
  }

  .chart-bar {
    max-width: 60rpx;
    margin: 0 2rpx;
  }

  .bar-floor {
    font-size: 18rpx;
  }

  .bar-value {
    font-size: 16rpx;
    top: -20rpx;
    padding: 1rpx 6rpx;
  }
}

// 动画效果
.overview-card,
.info-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 微信小程序兼容的简单动画效果
.routes-grid {
  transition: opacity 0.3s ease;
}

.route-details {
  transition: opacity 0.2s ease;
}

// 箭头旋转动画 - 微信小程序兼容
.route-toggle wd-icon {
  transition: transform 0.3s ease;
}

// 悬停效果优化
.route-detail-item:hover {
  background: rgba(19, 194, 194, 0.06);
  transform: translateX(4rpx);
  transition: all 0.2s ease;
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .material-detail {
    background: #1a1a1a;
  }

  .overview-card,
  .info-card {
    background: #2a2a2a;
    color: #ffffff;
  }

  .zone-title,
  .header-title,
  .item-value,
  .block-title,
  .bar-floor {
    color: #ffffff;
  }

  .zone-subtitle,
  .item-label,
  .y-axis-label,
  .x-axis-label {
    color: #cccccc;
  }

  .block-subtitle,
  .bar-value {
    color: #8b5cf6;
  }

  .bar-fill {
    background: linear-gradient(180deg, #8b5cf6 0%, #a855f7 50%, #c084fc 100%);
  }
}
</style>
<style lang="less">
.u2-popup {
  .wd-popup__close {
    color: #3a3a3a !important;
  }
}

// 泵房弹窗样式
.pump-house-popup {
  padding: 24rpx;
  height: 100%;
  display: flex;
  flex-direction: column;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;
    padding-bottom: 16rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .header-title {
      display: flex;
      align-items: center;
      gap: 8rpx;
      font-size: 32rpx;
      font-weight: 600;
      color: #1a1a1a;
    }

    .header-count {
      font-size: 24rpx;
      color: #666;
      background: linear-gradient(135deg, #e6fffa, #ccfbf1);
      color: #0f766e;
      padding: 6rpx 12rpx;
      border-radius: 16rpx;
      border: 1rpx solid rgba(19, 194, 194, 0.2);
      margin-right: 60rpx;
    }
  }

  .pump-house-list {
    flex: 1;
    overflow-y: auto;

    .pump-house-item {
      display: flex;
      align-items: center;
      background: linear-gradient(135deg, #fff 0%, #fafbfc 100%);
      border-radius: 12rpx;
      padding: 16rpx 20rpx;
      margin-bottom: 12rpx;
      border: 1rpx solid rgba(19, 194, 194, 0.1);
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        transform: translateY(-1rpx);
        box-shadow: 0 4rpx 16rpx rgba(19, 194, 194, 0.15);
        border-color: rgba(19, 194, 194, 0.3);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 2rpx 8rpx rgba(19, 194, 194, 0.1);
      }

      .item-icon {
        width: 32rpx;
        height: 32rpx;
        border-radius: 8rpx;
        background: linear-gradient(135deg, rgba(19, 194, 194, 0.15), rgba(19, 194, 194, 0.25));
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16rpx;
        flex-shrink: 0;
      }

      .item-content {
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .item-title {
          font-size: 28rpx;
          font-weight: 600;
          color: #1a1a1a;
          margin-right: 16rpx;
        }

        .item-status {
          display: flex;
          gap: 8rpx;

          .status-badge {
            padding: 4rpx 8rpx;
            border-radius: 12rpx;
            font-size: 20rpx;
            font-weight: 500;

            // 进度状态样式
            &.status-completed {
              background: #dcfce7;
              color: #166534;
              border: 1rpx solid #bbf7d0;
            }

            &.status-progress {
              background: #dbeafe;
              color: #1e40af;
              border: 1rpx solid #bfdbfe;
            }

            &.status-pending {
              background: #fef3c7;
              color: #92400e;
              border: 1rpx solid #fde68a;
            }

            &.status-paused {
              background: #fee2e2;
              color: #991b1b;
              border: 1rpx solid #fecaca;
            }

            // 改造状态样式
            &.state-completed {
              background: #dcfce7;
              color: #166534;
              border: 1rpx solid #bbf7d0;
            }

            &.state-progress {
              background: #dbeafe;
              color: #1e40af;
              border: 1rpx solid #bfdbfe;
            }

            &.state-pending {
              background: #fef3c7;
              color: #92400e;
              border: 1rpx solid #fde68a;
            }

            &.state-none {
              background: #f3f4f6;
              color: #6b7280;
              border: 1rpx solid #e5e7eb;
            }

            &.status-default,
            &.state-default {
              background: #f3f4f6;
              color: #6b7280;
              border: 1rpx solid #e5e7eb;
            }
          }
        }
      }

      .item-arrow {
        margin-left: 12rpx;
        opacity: 0.6;
        flex-shrink: 0;
      }
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 60rpx 20rpx;

      .empty-icon {
        margin-bottom: 16rpx;
        opacity: 0.5;
      }

      .empty-text {
        font-size: 24rpx;
        color: #999;
        font-weight: 500;
      }
    }
  }
}

.icon-wrapper-disabled {
  opacity: 0.5;
  pointer-events: none;
  background: linear-gradient(135deg, #ccc 0%, #999 100%) !important;

  &:hover {
    transform: none;
    box-shadow: 0 8rpx 32rpx rgba(204, 204, 204, 0.3), 0 2rpx 8rpx rgba(153, 153, 153, 0.2);
    background: linear-gradient(135deg, #ccc 0%, #999 100%);
  }

  .nav-icon-wrapper {
    background: rgba(255, 255, 255, 0.1);

    &:hover {
      transform: none;
      background: rgba(255, 255, 255, 0.1);
    }
  }

  .nav-text {
    color: rgba(255, 255, 255, 0.7);
  }
}
</style>
