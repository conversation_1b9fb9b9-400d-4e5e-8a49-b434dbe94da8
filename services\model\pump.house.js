import { nodeRequest } from '../index'

class PumpHouse {
  list(params) {
    return nodeRequest.get('/nodeServer/pumpHouse/wxlist', { params })
  }
  create(data) {
    return nodeRequest.post('/nodeServer/pumpHouse', data)
  }
  update(data) {
    return nodeRequest.put('/nodeServer/pumpHouse', data)
  }
  detail(pumpRoomNumber) {
    return nodeRequest.get(`/nodeServer/pumpHouse/detail/${pumpRoomNumber}`)
  }
  createNode(data) {
    return nodeRequest.post('/nodeServer/pumpHouse/node', data)
  }
  updateNode(data) {
    return nodeRequest.put('/nodeServer/pumpHouse/node', data)
  }

  nodeList(pumpRoomNumber) {
    return nodeRequest.get(`/nodeServer/pumpHouse/node/list/${pumpRoomNumber}`)
  }
  nodeDetail(pumpRoomNumber, nodeNum) {
    return nodeRequest.get(`/nodeServer/pumpHouse/node/${pumpRoomNumber}?Node=${nodeNum}`)
  }
  seek(pumpHouseName) {
    return nodeRequest.get(`/nodeServer/pumpHouse/seek?pumpHouseName=${pumpHouseName}`)
  }
  seekScope(ZoneCode) {
    return nodeRequest.get(`/nodeServer/pumpHouse/seekScope?ZoneCode=${ZoneCode}`)
  }
  pendingList(end) {
    return nodeRequest.get(`/nodeServer/pumpHouse/task/pendingList?end=${end}`)
  }
  taskList() {
    return nodeRequest.get(`/nodeServer/pumpHouse/task/list`)
  }
}

export const PumpHouseApi = new PumpHouse()
